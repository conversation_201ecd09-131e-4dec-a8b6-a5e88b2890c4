/**
 * Dependency updater for upgrading package dependencies
 * Uses appropriate package managers and handles dependency conflicts
 */

import { spawn } from "child_process";
import { join } from "path";

import type {
  DependencyUpdate,
  PackageJson,
} from "../types.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { Logger } from "./logger.js";

export class DependencyUpdater {
  private fsManager: FileSystemManager;

  constructor() {
    this.fsManager = new FileSystemManager();
  }

  /**
   * Update dependencies in package.json and install them
   */
  async updateDependencies(projectPath: string, updates: DependencyUpdate[]): Promise<void> {
    if (updates.length === 0) {
      Logger.info("No dependency updates required");
      return;
    }

    Logger.info(`Updating ${updates.length} dependencies...`);

    // Update package.json
    await this.updatePackageJson(projectPath, updates);

    // Detect and use appropriate package manager
    const packageManager = await this.detectPackageManager(projectPath);
    Logger.info(`Using package manager: ${packageManager}`);

    // Install dependencies
    await this.installDependencies(projectPath, packageManager);

    Logger.info("Dependencies updated successfully");
  }

  /**
   * Update package.json file with new dependency versions
   */
  private async updatePackageJson(projectPath: string, updates: DependencyUpdate[]): Promise<void> {
    const packageJsonPath = join(projectPath, "package.json");
    
    if (!this.fsManager.fileExists(packageJsonPath)) {
      throw new Error("package.json not found");
    }

    const content = await this.fsManager.readFile(packageJsonPath);
    const packageData: PackageJson = JSON.parse(content);

    // Apply updates
    for (const update of updates) {
      if (update.type === "dependencies") {
        if (!packageData.dependencies) {
          packageData.dependencies = {};
        }
        packageData.dependencies[update.name] = update.targetVersion;
        Logger.info(`Updated ${update.name}: ${update.currentVersion} → ${update.targetVersion}`);
      } else if (update.type === "devDependencies") {
        if (!packageData.devDependencies) {
          packageData.devDependencies = {};
        }
        packageData.devDependencies[update.name] = update.targetVersion;
        Logger.info(`Updated ${update.name} (dev): ${update.currentVersion} → ${update.targetVersion}`);
      }
    }

    // Write updated package.json
    const updatedContent = JSON.stringify(packageData, null, 2) + "\n";
    await this.fsManager.writeFile(packageJsonPath, updatedContent);
    Logger.info("package.json updated");
  }

  /**
   * Detect package manager based on lock files and package.json
   */
  private async detectPackageManager(projectPath: string): Promise<"npm" | "yarn" | "pnpm"> {
    // Check for lock files
    if (this.fsManager.fileExists(join(projectPath, "pnpm-lock.yaml"))) {
      return "pnpm";
    }
    
    if (this.fsManager.fileExists(join(projectPath, "yarn.lock"))) {
      return "yarn";
    }
    
    if (this.fsManager.fileExists(join(projectPath, "package-lock.json"))) {
      return "npm";
    }

    // Check package.json for packageManager field
    try {
      const packageJsonPath = join(projectPath, "package.json");
      if (this.fsManager.fileExists(packageJsonPath)) {
        const content = JSON.parse(await this.fsManager.readFile(packageJsonPath));
        if (content.packageManager) {
          if (content.packageManager.startsWith("pnpm")) return "pnpm";
          if (content.packageManager.startsWith("yarn")) return "yarn";
          if (content.packageManager.startsWith("npm")) return "npm";
        }
      }
    } catch (error) {
      Logger.warning("Failed to read package.json for package manager detection");
    }

    // Default to npm
    return "npm";
  }

  /**
   * Install dependencies using the specified package manager
   */
  private async installDependencies(projectPath: string, packageManager: "npm" | "yarn" | "pnpm"): Promise<void> {
    return new Promise((resolve, reject) => {
      const commands: Record<string, [string, string[]]> = {
        npm: ["npm", ["install"]],
        yarn: ["yarn", ["install"]],
        pnpm: ["pnpm", ["install"]],
      };

      const [command, args] = commands[packageManager];

      Logger.info(`Running: ${command} ${args.join(" ")}`);

      const child = spawn(command, args, {
        cwd: projectPath,
        stdio: ["inherit", "pipe", "pipe"],
        shell: process.platform === "win32",
      });

      let stdout = "";
      let stderr = "";

      child.stdout?.on("data", (data: any) => {
        stdout += data.toString();
      });

      child.stderr?.on("data", (data: any) => {
        stderr += data.toString();
      });

      child.on("close", (code: number | null) => {
        if (code === 0) {
          Logger.info("Dependencies installed successfully");
          resolve();
        } else {
          Logger.error(`Package installation failed with code ${code}`);
          if (stderr) {
            Logger.error(`Error output: ${stderr}`);
          }
          reject(new Error(`Package installation failed: ${stderr || `Exit code ${code}`}`));
        }
      });

      child.on("error", (error: Error) => {
        Logger.error(`Failed to start package manager: ${error.message}`);
        reject(error);
      });
    });
  }

  /**
   * Generate dependency updates from template comparison
   */
  generateDependencyUpdates(
    projectDependencies: Record<string, string>,
    projectDevDependencies: Record<string, string>,
    templateDependencies: Record<string, string>,
    templateDevDependencies: Record<string, string>
  ): DependencyUpdate[] {
    const updates: DependencyUpdate[] = [];

    // Check regular dependencies
    for (const [name, templateVersion] of Object.entries(templateDependencies)) {
      const currentVersion = projectDependencies[name];
      
      if (!currentVersion) {
        // New dependency
        updates.push({
          name,
          currentVersion: "not installed",
          targetVersion: templateVersion,
          type: "dependencies",
          reason: "New dependency from template",
        });
      } else if (currentVersion !== templateVersion) {
        // Version update
        updates.push({
          name,
          currentVersion,
          targetVersion: templateVersion,
          type: "dependencies",
          reason: "Version update from template",
        });
      }
    }

    // Check dev dependencies
    for (const [name, templateVersion] of Object.entries(templateDevDependencies)) {
      const currentVersion = projectDevDependencies[name];
      
      if (!currentVersion) {
        // New dev dependency
        updates.push({
          name,
          currentVersion: "not installed",
          targetVersion: templateVersion,
          type: "devDependencies",
          reason: "New dev dependency from template",
        });
      } else if (currentVersion !== templateVersion) {
        // Version update
        updates.push({
          name,
          currentVersion,
          targetVersion: templateVersion,
          type: "devDependencies",
          reason: "Dev dependency version update from template",
        });
      }
    }

    return updates;
  }

  /**
   * Validate dependency versions
   */
  validateDependencyVersions(updates: DependencyUpdate[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    for (const update of updates) {
      // Basic version format validation
      if (!this.isValidVersionFormat(update.targetVersion)) {
        errors.push(`Invalid version format for ${update.name}: ${update.targetVersion}`);
      }

      // Check for potential breaking changes (major version updates)
      if (this.isMajorVersionUpdate(update.currentVersion, update.targetVersion)) {
        Logger.warning(`Major version update detected for ${update.name}: ${update.currentVersion} → ${update.targetVersion}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Check if version format is valid
   */
  private isValidVersionFormat(version: string): boolean {
    // Allow semver patterns and common prefixes
    const versionPattern = /^[\^~]?\d+\.\d+\.\d+(-[\w\.-]+)?(\+[\w\.-]+)?$/;
    return versionPattern.test(version) || version === "latest" || version === "next";
  }

  /**
   * Check if this is a major version update
   */
  private isMajorVersionUpdate(currentVersion: string, targetVersion: string): boolean {
    try {
      const currentMajor = parseInt(currentVersion.replace(/[^\d]/, ""));
      const targetMajor = parseInt(targetVersion.replace(/[^\d]/, ""));
      return targetMajor > currentMajor;
    } catch {
      return false;
    }
  }
}

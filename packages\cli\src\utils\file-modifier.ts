/**
 * File modifier for applying upgrade changes to source files
 * Uses LLM integration to intelligently modify files while preserving customizations
 */

import { join } from "path";

import { Agent, run } from "@openai/agents";

import type {
  FileDifference,
  FileModification,
} from "../types.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { Logger } from "./logger.js";

export class FileModifier {
  private fsManager: FileSystemManager;
  private agent: Agent;

  constructor() {
    this.fsManager = new FileSystemManager();
    
    // Initialize the LLM agent for file modifications
    this.agent = new Agent({
      name: "FileModifier",
      instructions: `You are an expert TypeScript/React developer specializing in CSCS Agent projects.
        Your task is to apply upgrade changes to source files while preserving user customizations.

        When modifying files:
        1. Preserve all user customizations and custom code
        2. Apply only the necessary changes for compatibility
        3. Maintain code style and formatting
        4. Ensure TypeScript syntax correctness
        5. Keep imports and exports intact unless specifically changed
        6. Preserve comments and documentation

        Always return the complete modified file content without explanations.
        If a change cannot be safely applied, return the original content unchanged.`,
      model: process.env.OPENAI_MODEL || "gpt-4o",
    });
  }

  /**
   * Apply file modifications
   */
  async applyModifications(
    projectPath: string,
    modifications: FileModification[],
    createBackups: boolean = true
  ): Promise<void> {
    if (modifications.length === 0) {
      Logger.info("No file modifications required");
      return;
    }

    Logger.info(`Applying ${modifications.length} file modifications...`);

    for (const modification of modifications) {
      await this.applyModification(projectPath, modification, createBackups);
    }

    Logger.info("File modifications completed");
  }

  /**
   * Apply a single file modification
   */
  private async applyModification(
    projectPath: string,
    modification: FileModification,
    createBackup: boolean
  ): Promise<void> {
    const fullPath = join(projectPath, modification.filePath);

    try {
      if (modification.action === "create") {
        await this.createFile(fullPath, modification);
      } else if (modification.action === "update") {
        await this.updateFile(fullPath, modification, createBackup);
      } else if (modification.action === "backup") {
        await this.backupFile(fullPath);
      }

      Logger.info(`Applied modification: ${modification.description}`);
    } catch (error) {
      Logger.error(`Failed to apply modification to ${modification.filePath}: ${error instanceof Error ? error.message : "Unknown error"}`);
      throw error;
    }
  }

  /**
   * Create a new file
   */
  private async createFile(filePath: string, modification: FileModification): Promise<void> {
    if (this.fsManager.fileExists(filePath)) {
      Logger.warning(`File already exists, skipping creation: ${filePath}`);
      return;
    }

    // For new files, we would typically use template content
    // This is a placeholder - in practice, you'd get the content from the template
    const content = `// Generated file - ${modification.description}\n`;
    
    await this.fsManager.writeFile(filePath, content);
    Logger.info(`Created file: ${filePath}`);
  }

  /**
   * Update an existing file
   */
  private async updateFile(filePath: string, modification: FileModification, createBackup: boolean): Promise<void> {
    if (!this.fsManager.fileExists(filePath)) {
      Logger.warning(`File does not exist, cannot update: ${filePath}`);
      return;
    }

    // Create backup if requested
    if (createBackup) {
      await this.fsManager.backupFile(filePath);
    }

    const originalContent = await this.fsManager.readFile(filePath);
    
    // Apply changes using LLM
    const modifiedContent = await this.applyChangesWithLLM(
      originalContent,
      modification.changes,
      filePath
    );

    if (modifiedContent !== originalContent) {
      await this.fsManager.writeFile(filePath, modifiedContent);
      Logger.info(`Updated file: ${filePath}`);
    } else {
      Logger.info(`No changes needed for: ${filePath}`);
    }
  }

  /**
   * Backup a file
   */
  private async backupFile(filePath: string): Promise<void> {
    if (!this.fsManager.fileExists(filePath)) {
      Logger.warning(`File does not exist, cannot backup: ${filePath}`);
      return;
    }

    await this.fsManager.backupFile(filePath);
    Logger.info(`Backed up file: ${filePath}`);
  }

  /**
   * Apply changes to file content using LLM
   */
  private async applyChangesWithLLM(
    originalContent: string,
    changes: string[],
    filePath: string
  ): Promise<string> {
    try {
      const fileName = filePath.split(/[/\\]/).pop() || "file";
      
      const prompt = `Apply the following changes to this ${fileName} file while preserving all user customizations:

Original file content:
\`\`\`
${originalContent}
\`\`\`

Changes to apply:
${changes.map((change, index) => `${index + 1}. ${change}`).join("\n")}

Requirements:
1. Apply only the specified changes
2. Preserve all existing user customizations
3. Maintain code style and formatting
4. Ensure syntax correctness
5. Keep all imports and exports unless specifically changed
6. Preserve comments and documentation

Return the complete modified file content:`;

      const result = await run(this.agent, prompt);
      
      if (!result.finalOutput || typeof result.finalOutput !== "string") {
        Logger.warning(`LLM modification failed for ${filePath}, keeping original content`);
        return originalContent;
      }

      // Extract code from response (remove any markdown formatting)
      let modifiedContent = result.finalOutput;
      
      // Remove markdown code blocks if present
      const codeBlockMatch = modifiedContent.match(/```[\s\S]*?\n([\s\S]*?)```/);
      if (codeBlockMatch) {
        modifiedContent = codeBlockMatch[1];
      }

      return modifiedContent.trim();
    } catch (error) {
      Logger.warning(`LLM modification failed for ${filePath}: ${error instanceof Error ? error.message : "Unknown error"}`);
      return originalContent;
    }
  }

  /**
   * Generate file modifications from template comparison differences
   */
  generateFileModifications(
    filePath: string,
    differences: FileDifference[]
  ): FileModification[] {
    const modifications: FileModification[] = [];

    if (differences.length === 0) {
      return modifications;
    }

    // Group changes by action type
    const changes: string[] = [];
    
    for (const diff of differences) {
      switch (diff.action) {
        case "add":
          changes.push(`Add ${diff.type}: ${diff.description} (use template value: ${diff.templateValue})`);
          break;
        case "update":
          changes.push(`Update ${diff.type}: ${diff.description} (change from "${diff.projectValue}" to "${diff.templateValue}")`);
          break;
        case "remove":
          changes.push(`Remove ${diff.type}: ${diff.description} (current value: ${diff.projectValue})`);
          break;
        case "merge":
          changes.push(`Merge ${diff.type}: ${diff.description} (combine project value "${diff.projectValue}" with template value "${diff.templateValue}")`);
          break;
      }
    }

    if (changes.length > 0) {
      modifications.push({
        filePath,
        action: "update",
        description: `Apply ${differences.length} changes to ${filePath}`,
        changes,
      });
    }

    return modifications;
  }

  /**
   * Validate file modifications before applying
   */
  validateModifications(modifications: FileModification[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    for (const modification of modifications) {
      // Check if file path is valid
      if (!modification.filePath || modification.filePath.trim() === "") {
        errors.push("Invalid file path in modification");
        continue;
      }

      // Check if action is valid
      if (!["create", "update", "backup"].includes(modification.action)) {
        errors.push(`Invalid action "${modification.action}" for ${modification.filePath}`);
      }

      // Check if description is provided
      if (!modification.description || modification.description.trim() === "") {
        errors.push(`Missing description for modification of ${modification.filePath}`);
      }

      // For update actions, check if changes are provided
      if (modification.action === "update" && (!modification.changes || modification.changes.length === 0)) {
        errors.push(`No changes specified for update of ${modification.filePath}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Preview modifications without applying them
   */
  async previewModifications(
    projectPath: string,
    modifications: FileModification[]
  ): Promise<{ filePath: string; preview: string }[]> {
    const previews: { filePath: string; preview: string }[] = [];

    for (const modification of modifications) {
      const fullPath = join(projectPath, modification.filePath);
      
      if (modification.action === "update" && this.fsManager.fileExists(fullPath)) {
        const originalContent = await this.fsManager.readFile(fullPath);
        const previewContent = await this.applyChangesWithLLM(
          originalContent,
          modification.changes,
          fullPath
        );
        
        previews.push({
          filePath: modification.filePath,
          preview: previewContent,
        });
      } else if (modification.action === "create") {
        previews.push({
          filePath: modification.filePath,
          preview: `// New file will be created: ${modification.description}`,
        });
      }
    }

    return previews;
  }
}

/**
 * Type definitions for CSCS Agent CLI
 */

export interface TemplateMetadata {
  name: string;
  description: string;
  version: string;
  valid: boolean;
  path: string;
}

export interface ProjectOptions {
  name: string;
  template: string;
  directory?: string;
  skipInstall?: boolean;
  packageManager?: "npm" | "yarn" | "pnpm";
}

export interface CreateCommandOptions {
  template?: string;
  directory?: string;
  skipInstall?: boolean;
  packageManager?: string;
  interactive?: boolean;
}

export interface PackageJson {
  name: string;
  version: string;
  description?: string;
  private?: boolean;
  type?: string;
  scripts?: Record<string, string>;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  [key: string]: any;
}

export interface GenerateCommandOptions {
  name?: string;
  targetPath?: string;
  description?: string;
  interactive?: boolean;
  props?: string;
}

export interface WidgetGenerationOptions {
  name: string;
  type: string;
  targetPath: string;
  description?: string;
  props?: Record<string, any>;
  placement: "message" | "sender" | "sidePanel";
  slot?: "blocks" | "header" | "footer" | "headerPanel" | "render";
}

export interface ProjectStructure {
  hasAgentConfig: boolean;
  agentConfigPath: string;
  widgetsDir: string;
  srcDir: string;
  packageJsonPath: string;
}

export interface ValidationResult {
  valid: boolean;
  error?: string;
}

export interface CliConfig {
  templatesDir: string;
  defaultTemplate: string;
  supportedPackageManagers: string[];
}

// Upgrade command types
export interface UpgradeCommandOptions {
  targetPath?: string;
  interactive?: boolean;
  skipBackup?: boolean;
  dryRun?: boolean;
  force?: boolean;
}

export interface FileAnalysisResult {
  filePath: string;
  content: string;
  exists: boolean;
  analysis?: string;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
}

export interface ProjectAnalysisResult {
  packageJson: FileAnalysisResult;
  mainTsx: FileAnalysisResult;
  agentConfig: FileAnalysisResult;
  additionalFiles: FileAnalysisResult[];
  projectPath: string;
  isValidAgentProject: boolean;
}

export interface TemplateComparisonResult {
  file: string;
  templateContent: string;
  projectContent: string;
  differences: FileDifference[];
  requiresUpdate: boolean;
  updatePriority: 'critical' | 'recommended' | 'optional';
}

export interface FileDifference {
  type: 'dependency' | 'configuration' | 'code' | 'structure';
  description: string;
  templateValue: any;
  projectValue: any;
  action: 'add' | 'update' | 'remove' | 'merge';
}

export interface UpgradeReport {
  projectAnalysis: ProjectAnalysisResult;
  templateComparisons: TemplateComparisonResult[];
  dependencyUpdates: DependencyUpdate[];
  fileModifications: FileModification[];
  summary: UpgradeSummary;
}

export interface DependencyUpdate {
  name: string;
  currentVersion: string;
  targetVersion: string;
  type: 'dependencies' | 'devDependencies';
  reason: string;
}

export interface FileModification {
  filePath: string;
  action: 'create' | 'update' | 'backup';
  description: string;
  changes: string[];
}

export interface UpgradeSummary {
  totalFiles: number;
  modifiedFiles: number;
  addedDependencies: number;
  updatedDependencies: number;
  removedDependencies: number;
  criticalChanges: number;
  warnings: string[];
  errors: string[];
}

export interface DiffLine {
  type: "added" | "removed" | "context";
  content: string;
  lineNumber?: number;
}

export interface DiffResult {
  filePath: string;
  originalContent: string;
  modifiedContent: string;
  diffLines: DiffLine[];
  hasChanges: boolean;
}

export interface DiffOptions {
  contextLines?: number;
  showLineNumbers?: boolean;
}

export type UserChoice = "confirm" | "revert" | "view";
